import json
from collections import Counter

def identify_system_type(input_text):
    """识别使用的是哪个system"""
    # main_system的特征文本
    main_system = "你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。"

    if main_system in input_text:
        return "main_system"
    else:
        # 检查是否包含其他systems的特征（使用更准确的特征文本）
        systems = [
            "你是一位精通小学科学教学内容分析，并擅长解决多分类任务的专家。",
            "你的专业领域涵盖小学科学课程解析，以及信息的多维度归类。",
            "你扮演着一名资深的小学科学教育顾问角色，专门负责课程内容的分析与多项归类。",
            "你集小学科学课程评估与多分类问题解决能力于一身，是一位复合型专家。",
            "你拥有对小学科学教育进行深度课程分析的专业知识，并且是一位处理多分类问题的能手。",
            "你在小学科学教育的课程分析方面是权威，同时也是解决多分类挑战的专家。",
            "作为一名资深专家，你能够运用科学教育知识对课程进行分析，并完成复杂的多分类任务。",
            "你的专长在于剖析小学科学的教学内容，并将其精确地匹配到多个预设类别中。",
            "你是小学科学课程分析及多任务分类领域的资深专家。",
            "你擅长分析小学科学的教学大纲，并能熟练地为内容进行多标签分类。"
        ]

        for i, system in enumerate(systems):
            if system in input_text:
                return f"system_{i+1}"

        return "unknown"

def validate_data():
    """验证数据的正确性"""
    print("=== 数据验证报告 ===\n")

    # 先统计数据量
    original_count = 0
    with open('sft_total_data.json', 'r', encoding='utf-8') as f:
        original_data = json.load(f)
        original_count = len(original_data)

    processed_count = 0
    with open('multi_system_3steps_sft_data.json', 'r', encoding='utf-8') as f:
        processed_data = json.load(f)
        processed_count = len(processed_data)
    
    print(f"原始数据条数: {original_count}")
    print(f"处理后数据条数: {processed_count}")
    
    # 1. 统计system使用情况
    print("\n=== 1. System使用统计 ===")
    system_counter = Counter()
    
    for item in processed_data:
        system_type = identify_system_type(item['input'])
        system_counter[system_type] += 1
    
    total_count = processed_count
    print(f"总计: {total_count} 条数据")
    
    for system, count in system_counter.most_common():
        percentage = count / total_count * 100
        print(f"{system}: {count} 条 ({percentage:.1f}%)")
    
    # 检查比例是否符合预期
    main_system_percentage = system_counter.get('main_system', 0) / total_count * 100
    print(f"\nmain_system占比: {main_system_percentage:.1f}% (目标: 50%)")
    
    other_systems_count = sum(count for system, count in system_counter.items() if system.startswith('system_'))
    other_systems_percentage = other_systems_count / total_count * 100
    print(f"其他systems总占比: {other_systems_percentage:.1f}% (目标: 50%)")

    # 2. 抽样检查
    print("\n=== 2. 抽样检查 ===")
    print("前5条数据的system类型:")
    for i, item in enumerate(processed_data[:5]):
        system_type = identify_system_type(item['input'])
        # 提取角色部分的前50个字符作为预览
        role_start = item['input'].find('# 角色\n') + len('# 角色\n')
        role_end = item['input'].find('\n# 任务')
        if role_end == -1:
            role_preview = item['input'][role_start:role_start+50] + "..."
        else:
            role_preview = item['input'][role_start:role_end][:50] + "..."
        print(f"第{i+1}条: {system_type}")
        print(f"  角色预览: {role_preview}")

    # 3. Unknown数据分析
    print("\n=== 3. Unknown数据分析 ===")
    unknown_samples = []
    for item in processed_data:
        if identify_system_type(item['input']) == 'unknown':
            # 提取角色部分的前100字符作为特征
            role_start = item['input'].find('# 角色\n') + len('# 角色\n')
            role_end = item['input'].find('\n# 任务')
            if role_end == -1:
                role_desc = item['input'][role_start:role_start+100]
            else:
                role_desc = item['input'][role_start:role_end][:100]
            unknown_samples.append(role_desc)

    # 统计不同的unknown类型
    unknown_types = Counter(unknown_samples)

    print(f"Unknown数据总数: {len(unknown_samples)}")
    if unknown_samples:
        print("Unknown类型分布:")
        for i, (desc, count) in enumerate(unknown_types.most_common(3)):
            print(f"类型{i+1} ({count}条): {desc}...")
            if i == 0:  # 显示第一种类型的完整样本
                for item in processed_data:
                    if identify_system_type(item['input']) == 'unknown' and desc in item['input']:
                        print(f"\n完整样本:")
                        print(item['input'][:300] + "...")
                        break
    else:
        print("✓ 没有发现unknown数据")

if __name__ == "__main__":
    validate_data()

import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy

from crawl_data.MultiTurn_dialogue_data.build_3steps_sft_data.build_multi_system_data import main_system


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag




def convert_sft_data(item):
    # 权重分配：main_system 50%, 其他10个systems各5%
    weights = [0.5] + [0.05] * 10

    # 为两个文本分别选择替换内容
    choices_1 = [main_system] + systems

    selected_system_1 = random.choices(choices_1, weights=weights)[0]

    # 获取原始input文本
    input_text = item["input"]

    # 替换两段目标文本
    new_input = input_text.replace(main_system, selected_system_1)

    return {
        "input": new_input,
        "target": json.dumps(item["target"], ensure_ascii=False)
    }, f"system1:{selected_system_1[:20]}..."

def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


# 处理多轮对话SFT数据的路径配置
input_file = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\build_multi_dialogue_sft_data\multi_dialogue_sft_data.json'
output_file = r'multi_system_dialogue_sft_data.json'

# data_dir = '/work1/data/fanzhang39/0630/jx/res'
# save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana


# main_system: 50%权重
main_system = """
你是一位精通小学（1-6年级）科学教育，并且擅长构建多轮对话的专家。
你的核心任务是模拟一位充满耐心、善于鼓励且智慧的科学老师和一位小学生的对话。
"""

# systems: 各5%权重
systems = [
    """你是一位学识渊博的小学科学教育家，同时也是一位对话设计大师。
    你的任务是扮演一位充满耐心、智慧且善于鼓励的科学老师，与一名小学生进行一场富有启发性的多轮对话。""",

    """请你化身为一名循循善诱的科学导师，与小学生进行一场多轮互动。
    要完成这个任务，你必须调用你作为小学科学教育专家和多轮对话构建者的全部知识。""",

    """你的角色是一名精通小学1-6年级科学课程，并擅长组织结构化对话的专家。
    你的核心工作是模拟一场生动的师生交流，在对话中，你将以一位耐心、智慧且懂得鼓励的老师形象出现。""",

    """你是一名在小学科学教育和对话构建方面有深厚造诣的专家。
    你的主要职责是生成一段高质量的模拟对话，在其中扮演一位耐心、智慧并积极鼓励学生的科学老师。""",

    """你的任务是模拟并输出一段完整的师生对话。你需以一位精通小学科学教育和多轮对话设计的专家身份，去演绎那个充满耐心、智慧、善于鼓励的老师角色。""",

    """请综合运用你在小学科学教育领域的专业知识和构建多轮对话的卓越能力，来完成一项核心任务：模拟一场对话，在对话中你将以一位充满耐心、智慧和鼓励精神的科学老师形象，与一名小学生进行互动。""",

    """你的专长在于小学科学教育和引导式对话设计。现在，请运用这些专长，模拟一位富有耐心、智慧且善于表扬的科学老师，与一名小学生进行多回合的交流。""",

    """以一位资深小学科学教育者和对话设计师的身份，你的任务是生成一段对话，在其中完美地模拟一位智慧、有耐心、懂鼓励的老师与学生的互动过程。""",

    """你的核心目标是产出一段高质量的师生多轮对话。为了达到这个目标，你必须扮演一位精通小学科学教育的专家级老师，这个老师角色需要充满耐心、智慧，并善于鼓励。""",

    """你是一名小学科学教育和对话流程构建的复合型专家。请你进入角色，模拟一位耐心、智慧且懂得因材施教的科学老师，与一名小学生展开多轮对话。"""
]

# 处理SFT数据
def process_sft_data():
    print(f"Processing SFT data from: {input_file}")

    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Total records: {len(data)}")

    res = []
    all_systems = []

    for item in data:

        converted_item, system = convert_sft_data(item)
        res.append(converted_item)
        all_systems.append(system)


    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in res:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

    print(f"Processed {len(res)} records")
    print(f"Output saved to: {output_file}")

    # 统计系统使用情况
    system_counter = Counter(all_systems)
    print("\nSystem usage statistics:")
    for system, count in system_counter.items():
        percentage = count / len(res) * 100
        print(f"{system}: {count} ({percentage:.1f}%)")


# def process_original_data():
#     print(data_dir)
#
#     total = 0
#     res = []
#     all_systems = []
#     files = os.listdir(data_dir)
#     files.sort()
#     for file in files:
#         print(file)
#         data_path = os.path.join(data_dir, file)
#         f = open(data_path, encoding='utf-8')
#         with Pool(64) as p:
#             all_data = p.imap(convert, f)
#             # print(len(all_data))
#             for data in all_data:
#                 total += 1
#                 if data:
#                     data, system = data
#                     all_systems.append(system)
#                     res.append(data)
#
#     print(len(res), total)
#     with open(save_file, 'w', encoding='utf-8') as f:
#         for item in res:
#             f.write(json.dumps(item, ensure_ascii=False) + '\n')
#     print(save_file)
#
#     system_counter = Counter(all_systems)
#     system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
#     print(system_counter)
#     print([i / len(res) * 100 for i in system_counter.values()])

# 主执行逻辑
if __name__ == "__main__":
    # 处理SFT数据
    process_sft_data()


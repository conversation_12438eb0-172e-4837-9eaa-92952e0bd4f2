'''
用三步法对数据
输入数据、answer进行问题诞生
跟v1_2比，加了fewshot，并且修改了v1.2字段随机的问题，加了prompt_item = dynamic_part + PROMPT_HEADER的逻辑

'''
import re
import json

PROMPT_HEADER_3_4 = """
# 约束条件
## 精确复制
先确定'# 核心概念的学业要求'相关的 `id`，输出的 `description` 文本，必须与列表中对应 `id` 的文本一字不差。严禁自行修改、总结或缩写任何文字。

# 核心概念的学业要求
|id|核心概念的学业要求|
|---|---|
|1|能举例说明物体具有长度、质量、体积、温度等特征|
|2|能说明某些材料的透光性、导电性等特性及其主要用途|
|3|能说出水有三种状态|
|4|能说出空气受热会膨胀|
|5|能说出风是空气流动的结果|
|6|能比较水的三种状态的不同点|
|7|能利用证据说明空气占据空间、充满各处的性质等|
|8|能使用简单的仪器测量物体的长度、体积、温度、质量等|
|9|能用简单的物理方法把两种混合在一起的物体分离|
|10|能在教师指导下设计方案，观察空气受热上升的现象|
|11|对常见物体的特征和常见材料的性能表现出探究兴趣|
|12|认识到观察和测量的重要性|
|13|观察和测量的结果需要如实记录|
|14|能按要求进行合作探究学习|
|15|意识到各种材料对人们生活的意义|
|16|知道物质具有固态、液态、气态三种状态；|
|17|认识物质在一定量水中的溶解程度；|
|18|能说明影响物质溶解快慢的常见因素；|
|19|知道有些物体的大小和形状发生改变时，其构成物质没有改变。|
|20|能归纳概括气体、液体、固体在质量、体积、形状等方面的简单特征。|
|21|初步建立直观模型解释物质在一定量水中的溶解程度|
|22|能设计对比实验探究物质在水中溶解快慢的影响因素|
|23|能用简单的文字和图画描述观察到的现象|
|24|能对影响因素进行大胆的推测|
|25|愿意分享自己的想法，乐于倾听他人观点，完善和改进探究活动。|
|26|能说明常见物体的运动方式和特点，知道速度可以描述物体运动的快慢|
|27|能说明形成简单电路的基本条件及控制方法，区别导体和绝缘体，知道安全用电|
|28|能识别生活中的光源，能解释影子形成的原因|
|29|能解释声音产生与物体振动的关系，以及声音高低、强弱的改变与振动变化的关系，能说明声音可以在不同物质中传播，了解生产生活中的噪声现象和防噪办法。|
|30|能利用磁铁的基本性质解释某些生活现象。|
|31|能对常见的物体运动形式进行分类，概括不同运动的特点|
|32|利用简单电路比较不同材料的导电性|
|33|能解释物体振动与声音产生及其变化之间的关系。|
|34|能在教师指导下设计实验，探究声音与物体振动的关系；|
|35|能用科学词汇、图示符号等表达物体运动的方式；|
|36|会用简单工具测量距离、时间等，会用简单的方式连接简单电路。|
|37|能在探究过程中描述现象并如实记录；|
|38|能在动手连接电路等活动中，耐心操作、反复检查；|
|39|关注生活中电的重要作用。|
|40|知道热胀冷缩的性质及其在生产生活中的应用实例；|
|41|能识别生活中各种能的形式，举例说明运动的物体具有能量。|
|42|能用实例归纳概括物体的热胀冷缩性质，能举例分析物体运动过程中有能的变化。|
|43|能设计实验，探究常见物体的热胀冷缩现象。|
|44|能利用科学词汇、图示符号等方式记录现象。|
|45|能正确叙述自己的探究过程与结论，能倾听别人的意见，能合作交流；|
|46|愿意用热胀冷缩等相关的原理解解决生活中的一些问题。|
|47|能比较生物与非生物的特征，说出生物与非生物的不同特点；|
|48|能描述常见动物、植物的共同特征；|
|49|能描述人体用于呼吸的器官、用于摄取养分的器官。|
|50|能根据某些特征，对动物进行分类；|
|51|能概括植物的某些共同特征。|
|52|能设计简单方案，探究水、阳光、空气、温度等变化对生物生存的影响；|
|53|通过查阅资料等方式，知道动物依赖植物筑巢或作为庇护所，列举相关实例。|
|54|在认识常见生物、认识生物生存行为的过程中，认识热爱自然、保护环境以及保护当地动植物资源的积极意义；|
|55|认识保护人体具有呼吸功能和摄取养分功能器官的重要性。|
|56|能说出植物和动物都有基本生存需要，认识到植物、动物的某些结构具有帮助其维持自身生存的相应功能。|
|57|能比较分析植物、动物生存需要的差异。|
|58|能设计简单方案并实施操作，搜集植物和动物生存、生长所需条件的证据。|
|59|在学习过程中，保持好奇心和探究热情，能与他人交流证据和观点。|
|60|认识不同环境下植物的外部形态具有不同特点，认识动物能对季节变化作出反应。|
|61|能分析不同环境中植物的不同外部形态特点对维持其生存的作用，能比较不同动物适应季节变化的方式对维持其生存的作用。|
|62|能收集生活在不同环境中的植物外部形态特征的信息，调查动物适应季节变化的方式。|
|63|能积极参与对自然规律的探究活动。|
|64|认识生物通过生殖、发育和遗传实现生命的延续。|
|65|能分析动植物生命周期不同阶段的相应特点。|
|66|能记录、整理和描述常见植物和动物从生到死的生命过程。|
|67|对栽培植物、饲养动物以及观察动植物的生命周期产生兴趣。|
|68|知道地球与月球、地球与太阳的关系，能说出月球表面的概况。|
|69|能在教师指导下，观察和归纳一天中物体影长的变化情况。|
|70|能在教师指导下，通过望远镜观察，结合图片资料或模拟实验，认识月球表面的概况；|
|71|初步具有从具体现象提出问题，并制订简单探究计划的能力。|
|72|能通过动画或利用图片资料，认识地球、月球和太阳的运动关系，具有根据事实提出观点的意识。|
|73|能读懂天气预报，知道天气是运动的；|
|74|能说出地球表面海陆分布的概况和主要水体类型；|
|75|知道土壤的主要成分，举例说出适合生长在不同土壤中的植物。|
|76|能在教师指导下，利用气象数据，描述一天中的气温变化，建立气象数据与天气状况之间的联系。|
|77|能在教师指导下，学会使用仪器测量气象数据；|
|78|通过实验，认识到土壤的主要成分，了解不同质地的土壤适宜生长的植物，初步具备记录实验过程、整理实验结果、得出实验结论和沟通交流的能力。|
|79|对天气、水体、岩石、土壤等事物具有好奇心和探究热情，乐于动手实验，如实记录观察结果，具有用事实说话的意识。|
|80|知道矿产、淡水、土壤等自然资源对生产和生活的重要性。|
|81|能在教师指导下，分析自然资源与生产和生活的关系。|
|82|能在教师指导下，设计调查活动，了解合理利用自然资源的措施；|
|83|具备收集和整理信息、得出结论和沟通交流的能力。|
|84|树立保护和节约自然资源的意识。|
|85|能举例说出海洋为人类提供的多种资源，知道我国常见的自然灾害及其对生产和生活的影响，知道人类不合理的开发活动对环境的影响。|
|86|能区别生活中的天然材料和人造材料，知道一些常见的技术产品和典型工程。|
|87|能归纳总结常见产品或工程的主要外在特征，能拆开简单产品并复原，能举例说出一些技术产品所涉及的科学概念或原理。|
|88|能在教师指导下，利用常用工具制作某种产品的简化实物模型，并能反映其中的部分科学原理。|
|89|具有参与技术与工程实践的兴趣。|
|90|能提出满足一定限制条件的简单设计问题和多种设计方案。|
|91|能用多种方式说明设计思路，选择适当的方案。|
|92|能完成实物模型制作，发现实物模型的不足并进行改进。|
|93|具有技术与工程方面的操作兴趣，愿意动手尝试。|

# 输入输出样例
## 原始问题
"回旋纸飞机怎么折才能百分百回旋？"
## 多轮对话
"<JSON>{"step": "问题识别与澄清", "role": "老师", "cont": "你这个问题特别有趣！我们先聚焦一下，折好的纸飞机飞起来的时候，它的翅膀看起来是直直的，还是弯弯的呀？", "display_cont": "折好的回旋纸飞机翅膀看起来是什么样的？"}</JSON>
<JSON>{"step": "", "role": "学生", "cont": "好像有点弯弯的。", "display_cont": ""}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "老师", "cont": "你观察得好仔细呀，就像小侦探一样！你玩过纸船吗，折纸船的时候，把纸的边边往里折，船就有形状了。折纸飞机和折纸船有点像，你觉得折飞机的时候把边边怎么折，能让它有弯弯的翅膀呢？", "display_cont": "折纸飞机像折纸船一样折边，怎么折能有弯弯翅膀？"}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "学生", "cont": "我觉得把边往中间多折一点可能行。", "display_cont": ""}</JSON>
<JSON>{"step": "问题优化", "role": "老师", "cont": "你的想法真棒！那我们来看看，如果把纸飞机边往中间多折一点，飞起来的时候它的回旋效果和正常折比，是不是能肉眼看到不一样呢？", "display_cont": "改变纸飞机折边方式，回旋效果肉眼可见有变化吗？"}</JSON>
<JSON>{"step": "问题优化", "role": "学生", "cont": "感觉会有变化。", "display_cont": ""}</JSON>
<JSON>{"step": "产出可探究问题", "role": "老师", "cont": "你真是太厉害了，一下就抓住了关键！我们找到了‘改变折边方式’这个动作和‘纸飞机回旋效果’这个现象。我们可以用一个‘改变什么，会发生什么变化吗？’的句式来问个问题，你来试试把它们放进去？", "display_cont": "改变______，______会变吗？"}</JSON>
<JSON>{"step": "产出可探究问题", "role": "学生", "cont": "改变纸飞机折边方式，它的回旋效果会变吗？", "display_cont": ""}</JSON>
<JSON>{"step": "对话结束与最终评价", "role": "老师", "cont": "祝贺你呀！你成功提出了一个可探究的科学问题，整个思考过程特别棒，就像真正的小科学家一样，以后肯定能发现更多有趣的科学奥秘！", "display_cont": ""}</JSON>"
## 输出
{
  "classifications": [
    {
      "id": 19,
      "description": "知道有些物体的大小和形状发生改变时，其构成物质没有改变。"
    },
    {
      "id": 24,
      "description": "能对影响因素进行大胆的推测"
    },
    {
      "id": 26,
      "description": "能说明常见物体的运动方式和特点，知道速度可以描述物体运动的快慢"
    },
    {
      "id": 90,
      "description": "能提出满足一定限制条件的简单设计问题和多种设计方案。"
    },
    {
      "id": 93,
      "description": "具有技术与工程方面的操作兴趣，愿意动手尝试。"
    }
  ]
}
"""

PROMPT_HEADER_1_2 = """
# 约束条件
## 精确复制
先确定'# 核心概念的学业要求'相关的 `id`，输出的 `description` 文本，必须与列表中对应 `id` 的文本一字不差。严禁自行修改、总结或缩写任何文字。

# 核心概念的学业要求
|id|核心概念的学业要求|
|---|---|
|1|能举例说出生活中常见物体和材料的外部特征|
|2|能说出空气和水的形态特点|
|3|知道金属是常见的材料|
|4|能依据一些外部特征对常见物体进行比较和分类|
|5|能利用感官和观察工具（如放大镜等）进行观察并描述|
|6|能口述或利用简单图形表达想法|
|7|能在好奇心驱使下，对事物外部特征表现出探究兴趣|
|8|能仔细观察和比较，如实表达观察到的现象|
|9|愿意使用工具辅助观察|
|10|知道常见物质有些能在水中溶解，有些则很难溶解|
|11|能依据一定的标准，对常见物质的溶解现象进行分类。|
|12|能操作简单的实验，观察和描述某些物质在水中的溶解现象。|
|13|对生活中的溶解现象产生探究兴趣，能客观描述观察到的现象。|
|14|能举例说明力可以改变物体的形状|
|15|能使用方位名词说出物体所处的位置和方向|
|16|能举例说明磁铁具有磁力|
|17|能根据效果区别推力和拉力|
|18|能通过动手操作，体验力对物体形状的改变|
|19|在教师指导下操作简单的实验，比较磁铁对不同物质的吸引作用。|
|20|在体验活动和动手操作中，能如实表达观察到的现象，具有对推力、拉力、磁力等现象的直觉兴趣|
|21|认识到学会描述方位在生活中的重要性。|
|22|认识周边常见的动物和植物，并简单描述其外部主要特征；|
|23|认识人体的感觉器官。|
|24|能结合动物和植物的外部特征，比较动物和植物的异同|
|25|能概括动物的某些共同特征。|
|26|能利用多种感官观察身边常见生物的外部形态特征。|
|27|通过观察，对常见的动物、植物的外在特征产生探究兴趣。|
|28|认识到植物、动物的生存需要外界环境的帮助。|
|29|能分析不同植物生存和生长的条件，能比较不同动物感知环境的器官。|
|30|能依据可观察的现象，描述植物生存和生长的条件。|
|31|对生物生长的自然现象感到好奇。|
|32|能描述太阳的位置变化和月亮的形状变化，能举例说出季节变化对动植物和人类生活的影响。|
|33|能在教师指导下，通过多种感官，观察并描述太阳东升西落、月亮形状变化和季节变化等自然现象，能利用太阳的位置辨认方向。|
|34|能在教师指导下，通过观察或利用图片资料，知道一个月内月亮形状的变化情况，初步具备分享和交流的能力。|
|35|能在好奇心驱使下，了解季节变化对动植物的影响|
|36|愿意参与观察和分享活动，认识自然现象的变化。|
|37|能说出常见的天气现象，知道天气变化的影响；|
|38|认识到土壤为众多动植物提供了生存场所。|
|39|能在教师指导下，识别不同天气对动植物和人类生活的影响。|
|40|能在教师指导下，通过口述、画图等方式，交流对天气和土壤的观察结果。|
|41|能在好奇心驱使下，对天气变化和土壤中的动植物表现出探究兴趣，乐于表达和分享。|
|42|认识地球是人类与动植物共同的家园，知道有的材料可以被回收利用。|
|43|能在教师指导下，用语言或画图的方式，表达人类生活与自然环境的关系。|
|44|能在教师指导下，初步具备收集和交流信息的能力。|
|45|关爱生命，树立节约资源、保护环境的意识。|
|46|能区别自然物和人造物，能描述常见简单科技产品的结构与功能；|
|47|知道科技产品为人们生活带来便利。|
|48|学会使用锤子、安全剪刀、放大镜等简单工具，能应用身边常见材料和简单工具制作简单的作品；|
|49|能借助工具进行科学观察并进行交流。|
|50|具有动手操作的兴趣。|
|51|能基于观察提出并描述一个简单的制作问题。|
|52|能利用具体形象思维进行设计，并用简单草图说出设计思路。|
|53|能使用常见的工具和材料制作简单实物模型，能发现实物模型的不足。|
|54|具有实物制作的兴趣，乐于表达、讲述自己的想法。|

# 输入输出样例
## 原始问题
"回旋纸飞机怎么折才能百分百回旋？"
## 多轮对话
"<JSON>{"step": "问题识别与澄清", "role": "老师", "cont": "你这个问题特别有趣！我们先聚焦一下，折好的纸飞机飞起来的时候，它的翅膀看起来是直直的，还是弯弯的呀？", "display_cont": "折好的回旋纸飞机翅膀看起来是什么样的？"}</JSON>
<JSON>{"step": "", "role": "学生", "cont": "好像有点弯弯的。", "display_cont": ""}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "老师", "cont": "你观察得好仔细呀，就像小侦探一样！你玩过纸船吗，折纸船的时候，把纸的边边往里折，船就有形状了。折纸飞机和折纸船有点像，你觉得折飞机的时候把边边怎么折，能让它有弯弯的翅膀呢？", "display_cont": "折纸飞机像折纸船一样折边，怎么折能有弯弯翅膀？"}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "学生", "cont": "我觉得把边往中间多折一点可能行。", "display_cont": ""}</JSON>
<JSON>{"step": "问题优化", "role": "老师", "cont": "你的想法真棒！那我们来看看，如果把纸飞机边往中间多折一点，飞起来的时候它的回旋效果和正常折比，是不是能肉眼看到不一样呢？", "display_cont": "改变纸飞机折边方式，回旋效果肉眼可见有变化吗？"}</JSON>
<JSON>{"step": "问题优化", "role": "学生", "cont": "感觉会有变化。", "display_cont": ""}</JSON>
<JSON>{"step": "产出可探究问题", "role": "老师", "cont": "你真是太厉害了，一下就抓住了关键！我们找到了‘改变折边方式’这个动作和‘纸飞机回旋效果’这个现象。我们可以用一个‘改变什么，会发生什么变化吗？’的句式来问个问题，你来试试把它们放进去？", "display_cont": "改变______，______会变吗？"}</JSON>
<JSON>{"step": "产出可探究问题", "role": "学生", "cont": "改变纸飞机折边方式，它的回旋效果会变吗？", "display_cont": ""}</JSON>
<JSON>{"step": "对话结束与最终评价", "role": "老师", "cont": "祝贺你呀！你成功提出了一个可探究的科学问题，整个思考过程特别棒，就像真正的小科学家一样，以后肯定能发现更多有趣的科学奥秘！", "display_cont": ""}</JSON>"
## 输出
{
  "classifications": [
    {
      "id": 8,
      "description": "能仔细观察和比较，如实表达观察到的现象"
    },
    {
      "id": 14,
      "description": "能举例说明力可以改变物体的形状"
    },
    {
      "id": 18,
      "description": "能通过动手操作，体验力对物体形状的改变"
    },
    {
      "id": 53,
      "description": "能使用常见的工具和材料制作简单实物模型，能发现实物模型的不足。"
    },
    {
      "id": 54,
      "description": "具有实物制作的兴趣，乐于表达、讲述自己的想法。"
    }
  ]
}
"""
#load数据
def load_data(input_path):
    all_data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            data = json.loads(line)
            all_data.append(data)
    return all_data

def extract_query(text):
    marker = "**[输入数据]:**\n"
    # try:
    if marker in text:
        after_marker = text.split(marker)[1]
    return after_marker
        # print(text.split(marker))
        # print(after_marker)
        # lines =
    # except Exception as e:
    #     print(e)


#提取这个路径的数据
def extract_data(all_data):
    extracted_data = []
    for data in all_data:
        data_query = data['query']
        query = extract_query(data_query)
        answer = data['answer']
        id = data['id']
        dynamic_part = f"""# 角色
你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。
# 任务
你的任务是深入分析一个学生提出的问题和一段相关的师生对话。然后，从下方提供的“# 核心概念的学业要求”列表中，精确地筛选出所有与对话内容相关的要求内容。
# 输入输出
## 输入
学生提出的初始问题和围绕该问题展开的师生多轮对话，这两个都是字符串的格式
### 学生的初始问题
{query}
### 师生多轮对话
{answer}
## 输出
最终输出是一个结构化的、合法的字段为classifications的JSON对象。除了这个JSON对象，不要附加任何解释、注释或其他文字。"""

        prompt_item = dynamic_part + PROMPT_HEADER_1_2
        extracted_data_item = {
            'id': id,
            'query': prompt_item,
        }
        extracted_data.append(extracted_data_item)
    # print(query)
    # print(answer)
    return extracted_data

# def prompt(data):
#     total_prompt = []
#     for idx, data_item in enumerate(data):
#         prompt_item = f"""
#
#         """
#         total_prompt.append(prompt_item)
#     return total_prompt

def write_data_to_json(output_path, data):
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False)+'\n')



if __name__ == '__main__':
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v2_1\资源教育-语言学习_1010_doubao-1.5-pro-32k-250115_周依凡_1754018694448_data_MultiTurnDialogue.json'
    output_path = r'data_3steps_1_2.json'
    all_data = load_data(input_path)
    extracted_data = extract_data(all_data)
    write_data_to_json(output_path, extracted_data)
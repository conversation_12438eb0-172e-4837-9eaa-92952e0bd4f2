"""
[
{
"input":query
"target":answer
}
]
"""
import json


def load_data(input_path):
    data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data

def bulid_sft_data(input_path,output_path):
    datas = load_data(input_path)
    sft_data = []
    for data in datas:

        sft_data_input = data.get('query')

        sft_data_target_lst = json.loads(data.get('answer')).get('classifications')

        sft_data_item = {
            'input': sft_data_input,
            'target': sft_data_target_lst
        }
        sft_data.append(sft_data_item)
    write_data(output_path,sft_data)




def write_data(output_path,data):
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(data, ensure_ascii=False))




if __name__ == '__main__':
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_3steps_v1_3\资源教育-语言学习_534_doubao-1.5-pro-32k-250115_周依凡_1754374494633_data_3steps_3_4_export_all_xz_doubao_1.5_32k.json'
    output_path = r'sft_total_data.json'
    load_data(input_path)
    bulid_sft_data(input_path,output_path)
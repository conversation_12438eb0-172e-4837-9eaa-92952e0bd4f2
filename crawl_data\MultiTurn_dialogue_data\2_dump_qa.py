import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag


def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


data_dir = '/work1/data/fanzhang39/0630/jx/res'
save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = "假设你是一位专业的科学老师，请你基于给定的【试题】、【答案】，生成当前讲解试题的解析。"
systems = [
    "假定您是一名专业的科学教师，请依据给定的【试题】、【答案】，完成当前试题讲解的解析内容。",
    "倘若您是一位具备专业素养的科学教师，那么请基于给定的【试题】与【答案】，撰写出针对当前试题的解析。",
    "假设您身为专业的科学授课教师，需根据所提供的【试题】和【答案】，生成此次试题的讲解分析。",
    "若您是一位专业的科学教师，现要求您以给定的【试题】、【答案】为基础，创作出当前试题的详细解析。",
    "假定您为专业的科学教师，需要依据已有的【试题】和【答案】，完成对当前试题的解析阐述工作。",
    "倘若您作为一名专业的科学教师，请根据给定的【试题】与【答案】，给出当前试题的讲解分析内容。",
    "假设您是专业的科学老师，应基于给定的【试题】、【答案】，生成适用于当前试题讲解的解析说明。",
    "若您身为专业的科学教师，需以给定的【试题】和【答案】为依据，完成当前试题的解析创作。",
    "假定您是一位科学专业教师，现请根据【试题】、【答案】，对当前试题进行解析生成讲解内容。",
    "倘若您作为专业的科学教师，要基于给定的【试题】、【答案】，形成当前试题的讲解解析。"
]

print(data_dir)

total = 0
res = []
all_systems = []
files = os.listdir(data_dir)
files.sort()
for file in files:
    print(file)
    data_path = os.path.join(data_dir, file)
    f = open(data_path, encoding='utf-8')
    with Pool(64) as p:
        all_data = p.imap(convert, f)
        # print(len(all_data))
        for data in all_data:
            total += 1
            if data:
                data, system = data
                all_systems.append(system)
                res.append(data)

# print(ans) 
# count = Counter(ans)
# print(len(res))
# print(total)
# print(count)

print(len(res), total)
with open(save_file, 'w', encoding='utf-8') as f:
    for item in res:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')
print(save_file)

system_counter = Counter(all_systems)
system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
print(system_counter)
print([i / len(res) * 100 for i in system_counter.values()])


'''
用三步法对数据
输入数据、answer进行问题诞生
'''
import re
import json

#load数据
def load_data(input_path):
    all_data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            data = json.loads(line)
            all_data.append(data)
    return all_data

def extract_query(text):
    marker = "**[输入数据]:**\n"
    # try:
    if marker in text:
        after_marker = text.split(marker)[1]
    return after_marker
        # print(text.split(marker))
        # print(after_marker)
        # lines =
    # except Exception as e:
    #     print(e)


#提取这个路径的数据
def extract_data(all_data):
    extracted_data = []
    for data in all_data:
        data_query = data['query']
        query = extract_query(data_query)
        answer = data['answer']
        id = data['id']
        prompt_item = f"""
# 角色
你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。
# 任务
你的任务是深入分析一个学生提出的初始问题{query}和一段围绕该问题展开的师生多轮对话{answer}。然后，根据对话中涉及的科学概念、探究方法、思维过程和科学态度，从下方提供的“# 核心概念的学业要求”列表中，筛选出所有几种最相关的学业要求。
# 输入输出
## 输入
学生提出的初始问题{query}和围绕该问题展开的师生多轮对话{answer}，这两个都是字符串的格式
## 输出
输出列表，列表包含几种最相关的要求，你将使用以下“# 核心概念的学业要求”列表作为唯一的分类标准。

# 核心概念的学业要求
|---|
| 能举例说明物体具有长度、质量、体积、温度等特征 |
| 能说明某些材料的透光性、导电性等特性及其主要用途 |
| 能说出水有三种状态 |
| 能说出空气受热会膨胀 |
| 能说出风是空气流动的结果 |
| 能比较水的三种状态的不同点 |
| 能利用证据说明空气占据空间、充满各处的性质等 |
| 能使用简单的仪器测量物体的长度、体积、温度、质量等 |
| 能用简单的物理方法把两种混合在一起的物体分离 |
| 能在教师指导下设计方案，观察空气受热上升的现象 |
| 对常见物体的特征和常见材料的性能表现出探究兴趣 |
| 认识到观察和测量的重要性 |
| 观察和测量的结果需要如实记录 |
| 能按要求进行合作探究学习 |
| 意识到各种材料对人们生活的意义 |
| 知道物质具有固态、液态、气态三种状态； |
| 认识物质在一定量水中的溶解程度； |
| 能说明影响物质溶解快慢的常见因素； |
| 知道有些物体的大小和形状发生改变时，其构成物质没有改变。 |
| 能归纳概括气体、液体、固体在质量、体积、形状等方面的简单特征。 |
| 初步建立直观模型解释物质在一定量水中的溶解程度 |
| 能设计对比实验探究物质在水中溶解快慢的影响因素 |
| 能用简单的文字和图画描述观察到的现象 |
| 能对影响因素进行大胆的推测 |
| 愿意分享自己的想法，乐于倾听他人观点，完善和改进探究活动。 |
| 能说明常见物体的运动方式和特点，知道速度可以描述物体运动的快慢 |
| 能说明形成简单电路的基本条件及控制方法，区别导体和绝缘体，知道安全用电 |
| 能识别生活中的光源，能解释影子形成的原因 |
| 能解释声音产生与物体振动的关系，以及声音高低、强弱的改变与振动变化的关系，能说明声音可以在不同物质中传播，了解生产生活中的噪声现象和防噪办法。 |
| 能利用磁铁的基本性质解释某些生活现象。 |
| 能对常见的物体运动形式进行分类，概括不同运动的特点 |
| 利用简单电路比较不同材料的导电性 |
| 能解释物体振动与声音产生及其变化之间的关系。 |
| 能在教师指导下设计实验，探究声音与物体振动的关系； |
| 能用科学词汇、图示符号等表达物体运动的方式； |
| 会用简单工具测量距离、时间等，会用简单的方式连接简单电路。 |
| 能在探究过程中描述现象并如实记录； |
| 能在动手连接电路等活动中，耐心操作、反复检查； |
| 关注生活中电的重要作用。 |
| 知道热胀冷缩的性质及其在生产生活中的应用实例； |
| 能识别生活中各种能的形式，举例说明运动的物体具有能量。 |
| 能用实例归纳概括物体的热胀冷缩性质，能举例分析物体运动过程中有能的变化。 |
| 能设计实验，探究常见物体的热胀冷缩现象。 |
| 能利用科学词汇、图示符号等方式记录现象。 |
| 能正确叙述自己的探究过程与结论，能倾听别人的意见，能合作交流； |
| 愿意用热胀冷缩等相关的原理解解决生活中的一些问题。 |
| 能比较生物与非生物的特征，说出生物与非生物的不同特点； |
| 能描述常见动物、植物的共同特征； |
| 能描述人体用于呼吸的器官、用于摄取养分的器官。 |
| 能根据某些特征，对动物进行分类； |
| 能概括植物的某些共同特征。 |
| 能设计简单方案，探究水、阳光、空气、温度等变化对生物生存的影响； |
| 通过查阅资料等方式，知道动物依赖植物筑巢或作为庇护所，列举相关实例。 |
| 在认识常见生物、认识生物生存行为的过程中，认识热爱自然、保护环境以及保护当地动植物资源的积极意义； |
| 认识保护人体具有呼吸功能和摄取养分功能器官的重要性。 |
| 能说出植物和动物都有基本生存需要，认识到植物、动物的某些结构具有帮助其维持自身生存的相应功能。 |
| 能比较分析植物、动物生存需要的差异。 |
| 能设计简单方案并实施操作，搜集植物和动物生存、生长所需条件的证据。 |
| 在学习过程中，保持好奇心和探究热情，能与他人交流证据和观点。 |
| 认识不同环境下植物的外部形态具有不同特点，认识动物能对季节变化作出反应。 |
| 能分析不同环境中植物的不同外部形态特点对维持其生存的作用，能比较不同动物适应季节变化的方式对维持其生存的作用。 |
| 能收集生活在不同环境中的植物外部形态特征的信息，调查动物适应季节变化的方式。 |
| 能积极参与对自然规律的探究活动。 |
| 认识生物通过生殖、发育和遗传实现生命的延续。 |
| 能分析动植物生命周期不同阶段的相应特点。 |
| 能记录、整理和描述常见植物和动物从生到死的生命过程。 |
| 对栽培植物、饲养动物以及观察动植物的生命周期产生兴趣。 |
| 知道地球与月球、地球与太阳的关系，能说出月球表面的概况。 |
| 能在教师指导下，观察和归纳一天中物体影长的变化情况。 |
| 能在教师指导下，通过望远镜观察，结合图片资料或模拟实验，认识月球表面的概况； |
| 初步具有从具体现象提出问题，并制订简单探究计划的能力。 |
| 能通过动画或利用图片资料，认识地球、月球和太阳的运动关系，具有根据事实提出观点的意识。 |
| 能读懂天气预报，知道天气是运动的； |
| 能说出地球表面海陆分布的概况和主要水体类型； |
| 知道土壤的主要成分，举例说出适合生长在不同土壤中的植物。 |
| 能在教师指导下，利用气象数据，描述一天中的气温变化，建立气象数据与天气状况之间的联系。 |
| 能在教师指导下，学会使用仪器测量气象数据； |
| 通过实验，认识到土壤的主要成分，了解不同质地的土壤适宜生长的植物，初步具备记录实验过程、整理实验结果、得出实验结论和沟通交流的能力。 |
| 对天气、水体、岩石、土壤等事物具有好奇心和探究热情，乐于动手实验，如实记录观察结果，具有用事实说话的意识。 |
| 知道矿产、淡水、土壤等自然资源对生产和生活的重要性。 |
| 能在教师指导下，分析自然资源与生产和生活的关系。 |
| 能在教师指导下，设计调查活动，了解合理利用自然资源的措施； |
| 具备收集和整理信息、得出结论和沟通交流的能力。 |
| 树立保护和节约自然资源的意识。 |
| 能举例说出海洋为人类提供的多种资源，知道我国常见的自然灾害及其对生产和生活的影响，知道人类不合理的开发活动对环境的影响。 |
| 能区别生活中的天然材料和人造材料，知道一些常见的技术产品和典型工程。 |
| 能归纳总结常见产品或工程的主要外在特征，能拆开简单产品并复原，能举例说出一些技术产品所涉及的科学概念或原理。 |
| 能在教师指导下，利用常用工具制作某种产品的简化实物模型，并能反映其中的部分科学原理。 |
| 具有参与技术与工程实践的兴趣。 |
| 能提出满足一定限制条件的简单设计问题和多种设计方案。 |
| 能用多种方式说明设计思路，选择适当的方案。 |
| 能完成实物模型制作，发现实物模型的不足并进行改进。 |
| 具有技术与工程方面的操作兴趣，愿意动手尝试。 |
"""


        extracted_data_item = {
            'id': id,
            'query': prompt_item,
        }
        extracted_data.append(extracted_data_item)
    # print(query)
    # print(answer)
    return extracted_data

# def prompt(data):
#     total_prompt = []
#     for idx, data_item in enumerate(data):
#         prompt_item = f"""
#
#         """
#         total_prompt.append(prompt_item)
#     return total_prompt

def write_data_to_json(output_path, data):
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False)+'\n')



if __name__ == '__main__':
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v2_1\资源教育-语言学习_1010_doubao-1.5-pro-32k-250115_周依凡_1754018694448_data_MultiTurnDialogue.json'
    output_path = r'data_3steps_3_4.json'
    all_data = load_data(input_path)
    extracted_data = extract_data(all_data)
    write_data_to_json(output_path, extracted_data)
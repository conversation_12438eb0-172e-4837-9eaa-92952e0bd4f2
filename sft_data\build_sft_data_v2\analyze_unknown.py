import json

def analyze_unknown_issue():
    """分析unknown问题的根本原因"""
    
    # 读取处理后的数据 - 只取前100条避免内存问题
    with open('multi_system_sft_data.json', 'r', encoding='utf-8') as f:
        all_data = json.load(f)
        processed_data = all_data[:500]  # 取前500条分析
    
    print("=== Unknown问题分析 ===\n")
    
    # 定义main_system特征
    main_system_marker = "你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家"
    
    # 定义systems特征（与build_multi_system_data.py中的实际内容对应）
    system_markers = [
        "你是一位杰出的少儿科普内容专家与科学知识精炼大师，专长是将复杂抽象、令人望而生畏的科学理论",  # system_0
        "作为顶尖的科学传播专家和内容整合大师，你拥有化繁为简的非凡能力",  # system_1
        "你身兼一流少儿科普作家与科学知识整合者的双重身份",  # system_2
        '你是一位能将科学语言巧妙"翻译"给孩子的专家，可把抽象枯燥的知识点用生动有趣的方式重新建构',  # system_3
        "作为卓越的青少年科普教育家，你能将复杂抽象的科学知识点",  # system_4
        "作为资深的少儿科普内容行家与科学知识整合专家",  # system_5
        "作为顶尖的少儿科普专家与知识整合精炼大师，你特别擅长将复杂的知识点",  # system_6
        "作为出色的科学传播者和知识精炼专家，你专为青少年和好奇的学习者服务",  # system_7
        "作为顶级的少儿科普内容塑造者，你能将严谨的科学知识与生动的叙事相结合",  # system_8
        '作为专业的科学知识"翻译家"，你能将科学家眼中的复杂世界，精准而又风趣地转述给充满好奇心的青少年们',  # system_9
    ]
    
    # 统计各种类型
    main_count = 0
    system_counts = [0] * 10
    unknown_count = 0
    unknown_samples = []
    
    for item in processed_data:
        input_text = item['input']
        
        # 检查是否是main_system
        if main_system_marker in input_text:
            main_count += 1
            continue
        
        # 检查是否是其他systems
        found = False
        for i, marker in enumerate(system_markers):
            if marker in input_text:
                system_counts[i] += 1
                found = True
                break
        
        if not found:
            unknown_count += 1
            if len(unknown_samples) < 3:  # 只保存前3个unknown样本
                # 提取任务描述部分
                task_start = input_text.find('# 任务描述\n') + len('# 任务描述\n')
                task_end = input_text.find('\n\n# 角色扮演')
                if task_end == -1:
                    task_end = len(input_text)
                task_desc = input_text[task_start:task_end].strip()
                unknown_samples.append(task_desc)
    
    # 输出统计结果
    print(f"样本总数: {len(processed_data)}")
    print(f"main_system: {main_count} ({main_count/len(processed_data)*100:.1f}%)")
    
    for i, count in enumerate(system_counts):
        if count > 0:
            print(f"system_{i}: {count} ({count/len(processed_data)*100:.1f}%)")
    
    print(f"unknown: {unknown_count} ({unknown_count/len(processed_data)*100:.1f}%)")
    
    # 分析unknown样本
    if unknown_samples:
        print(f"\n=== Unknown样本分析 ===")
        for i, sample in enumerate(unknown_samples):
            print(f"\nUnknown样本{i+1}:")
            print(f"长度: {len(sample)}")
            print(f"前100字符: {sample[:100]}...")
            
            # 检查是否包含特定关键词
            if "翻译" in sample:
                print("  → 包含'翻译'关键词")
            if "科普" in sample:
                print("  → 包含'科普'关键词")
            if "专家" in sample:
                print("  → 包含'专家'关键词")
            
            # 尝试匹配每个system marker的前50字符
            print("  → 与各system的匹配度:")
            for j, marker in enumerate(system_markers):
                if marker[:50] in sample:
                    print(f"    system_{j}: 匹配前50字符")
                elif marker[:30] in sample:
                    print(f"    system_{j}: 匹配前30字符")
                elif marker[:20] in sample:
                    print(f"    system_{j}: 匹配前20字符")

if __name__ == "__main__":
    analyze_unknown_issue()
